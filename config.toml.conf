# Example configuration using the new strategy system
# This allows comparing different investment approaches for the same ticker

# Define general parameters for the entire analysis
[general]
years_ago = 10
investment_amount = 1000
investment_kind = "monthly"
animation_duration_seconds = 15
fps = 30
tax_rate = 0.26375
tax_free_return_threshold_per_annu = 1000
save_data = false

# 🤖 Telegram Bot Configuration (Optional)
# Configure Telegram bot to receive logging information and notifications
# You can also use environment variables: TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID
[telegram]
# token = "YOUR_BOT_TOKEN_HERE"  # Get from @BotFather on Telegram
# chat_id = "YOUR_CHAT_ID_HERE"  # Your user ID or group chat ID
# enabled = false  # Set to true to enable Telegram notifications
interactive_menu = true  # Enable interactive menu at startup
interactive_timeout_minutes = 5  # Minutes to wait for user selection

# New strategy-based configuration
# Each strategy can use the same ticker with different parameters
[strategies]

# Apple without dividend reinvestment
[strategies.AAPL_no_reinvest]
ticker = "AAPL"
name = "Apple (No Dividend Reinvestment)"
reinvest_dividends = false
timing = "regular"

# Apple with dividend reinvestment
[strategies.AAPL_reinvest]
ticker = "AAPL"
name = "Apple (With Dividend Reinvestment)"
reinvest_dividends = true
timing = "regular"

# Microsoft without dividend reinvestment
[strategies.MSFT_no_reinvest]
ticker = "MSFT"
name = "Microsoft (No Dividend Reinvestment)"
reinvest_dividends = false
timing = "regular"

# Microsoft with dividend reinvestment
[strategies.MSFT_reinvest]
ticker = "MSFT"
name = "Microsoft (With Dividend Reinvestment)"
reinvest_dividends = true
timing = "regular"

# You could also add strategies for different timing approaches
# [strategies.AAPL_peaks]
# ticker = "AAPL"
# name = "Apple (Buy at Peaks)"
# reinvest_dividends = false
# timing = "peaks"

# [strategies.AAPL_lows]
# ticker = "AAPL"
# name = "Apple (Buy at Lows)"
# reinvest_dividends = false
# timing = "lows"
