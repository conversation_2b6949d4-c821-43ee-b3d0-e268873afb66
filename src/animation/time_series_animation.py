import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patheffects as path_effects
from matplotlib.animation import Func<PERSON>nimation
from matplotlib.ticker import <PERSON><PERSON><PERSON><PERSON>att<PERSON>
from itertools import cycle
import textwrap
import tempfile
from pathlib import Path
from typing import Optional
from src.logger import get_logger
from src.animation._easing_functions import EASING_FUNCTIONS
from src.music import MusicManager

LOGGER = get_logger(__name__)

# TODO: Make y-axis go with the animation, if numbers get bigger


def create_wealth_animation(
    wealth_df: pd.DataFrame,
    investment_years: float | int,
    filename: str = "wealth_animation.mp4",
    duration_sec: int = 8,
    fps: int = 30,
    title: str = "Wealth Projection Over Time",
    easing_function: str = None,
    music_filename: Optional[str] = None,
    music_dir: Optional[Path] = None,
):
    """Create a stylish wealth animation with glowing lines and non-linear pacing.

    Generates an animated visualization of wealth accumulation over time for
    multiple investments. The animation features glowing line effects, smooth
    easing transitions, and a summary table that fades in at the end showing
    final performance metrics. Optionally includes background music.

    Args:
        wealth_df (pd.DataFrame): DataFrame containing wealth data over time.
            Must include a "Total Investments" column and one or more columns
            for individual investment wealth projections.
        investment_years (float | int): Number of years covered by the investment
            period, used for calculating annualized returns.
        filename (str, optional): Output filename for the animation video.
            Defaults to "wealth_animation.mp4".
        duration_sec (int, optional): Duration of the main animation in seconds,
            excluding the summary fade-in period. Defaults to 8.
        fps (int, optional): Frames per second for the animation. Higher values
            create smoother animations but larger file sizes. Defaults to 30.
        title (str, optional): Title displayed at the top of the animation.
            Defaults to "Wealth Projection Over Time".
        easing_function (str, optional): Name of the easing function to apply
            for non-linear animation pacing. Must be a key in EASING_FUNCTIONS
            dictionary. If None, linear pacing is used. Defaults to None.
        music_filename (Optional[str], optional): Specific music file to use for
            background audio. If None, the first available music file is used.
            If no music files are available, no audio is added. Defaults to None.
        music_dir (Optional[Path], optional): Path to the music directory containing
            music files and creators.toml configuration. If None, defaults to
            'assets/music' relative to project root. Defaults to None.

    Returns:
        None: The function saves the animation to the specified filename and
            logs the creation process. If music is available, creates a version
            with background audio.

    Note:
        The animation includes a 2-second summary fade-in period after the main
        animation, showing final rankings, wealth values, CAGR, and volatility
        for each investment. Music integration requires FFmpeg and properly
        configured music files in the assets/music directory.
    """
    LOGGER.activate()

    # --- Data and Animation Setup ---
    investment_col_name = "Total Investments"
    investment_series = wealth_df[investment_col_name]
    stock_wealth_df = wealth_df.drop(columns=[investment_col_name])

    total_investment = investment_series.iloc[-1]

    first_investment_val = investment_series[investment_series > 0].iloc[0]

    SUMMARY_FADE_IN_SEC = 2
    main_animation_frames = duration_sec * fps
    summary_frames = SUMMARY_FADE_IN_SEC * fps
    total_animation_frames = main_animation_frames + summary_frames

    easing_function = EASING_FUNCTIONS.get(easing_function, lambda x: x)
    normalized_time = np.linspace(0, 1, num=main_animation_frames)
    eased_time = np.array([easing_function(t) for t in normalized_time])
    main_frame_indices = (eased_time * (len(wealth_df) - 1)).astype(int)

    # --- Calculate dynamic margins based on maximum wealth values ---
    max_wealth = stock_wealth_df.max().max()
    wealth_digits = len(f"{max_wealth:,.0f}".replace(",", ""))

    # Dynamic left margin: more digits = more space needed for y-axis labels
    # Base margin of 0.12, plus 0.015 for each digit beyond 6
    base_left_margin = 0.12
    extra_margin_per_digit = 0.015
    dynamic_left_margin = base_left_margin + max(
        0, (wealth_digits - 6) * extra_margin_per_digit
    )
    dynamic_left_margin = min(
        dynamic_left_margin, 0.25
    )  # Cap at 25% to preserve chart space

    # Platform-optimized margins to avoid UI intersections
    # Top margin increased to avoid TikTok input button and search
    # Bottom margin for timestamp and platform controls
    TOP_MARGIN = 0.88  # Reduced from 0.95 to avoid TikTok UI
    BOTTOM_MARGIN = 0.12  # Increased from 0.1 for better clearance
    RIGHT_MARGIN = 0.96  # Slight reduction to ensure timestamp visibility

    # --- Plot Setup ---
    plt.style.use("seaborn-v0_8-darkgrid")
    fig, ax = plt.subplots(figsize=(9, 16), dpi=300)
    fig.patch.set_facecolor(
        "#1a1f2e"
    )  # Lighter dark blue background - more appealing and less harsh
    ax.set_facecolor("#1a1f2e")

    # Enhanced color palette with maximum visual appeal and distinctiveness
    VIBRANT_COLORS = [
        "#00ff88",  # Electric green - highly distinctive and energetic
        "#ff4757",  # Bright red - strong contrast and attention-grabbing
        "#3742fa",  # Electric blue - professional but vibrant
        "#ffa502",  # Bright orange - warm and inviting
        "#ff6348",  # Coral - warm and modern
        "#7bed9f",  # Light green - fresh and appealing
        "#70a1ff",  # Sky blue - calming but vibrant
        "#5352ed",  # Purple - premium and trendy
        "#ff9ff3",  # Hot pink - eye-catching and modern
        "#2ed573",  # Lime green - energetic and fresh
    ]
    color_map = {
        ticker: color
        for ticker, color in zip(stock_wealth_df.columns, cycle(VIBRANT_COLORS))
    }

    # --- Create enhanced line groups for premium glow effect ---
    stock_line_groups = {}
    for ticker in stock_wealth_df.columns:
        color = color_map[ticker]
        # Enhanced glow effect with more layers for premium look
        glow_lines = [
            ax.plot([], [], color=color, lw=12, alpha=0.08, zorder=1)[0],  # Outer glow
            ax.plot([], [], color=color, lw=8, alpha=0.12, zorder=2)[0],  # Mid glow
            ax.plot([], [], color=color, lw=5, alpha=0.18, zorder=3)[0],  # Inner glow
            ax.plot([], [], color=color, lw=2.5, alpha=1.0, zorder=4, label=ticker)[
                0
            ],  # Core line
        ]
        stock_line_groups[ticker] = glow_lines

    # Enhanced investment line with subtle glow
    investment_line_glow = ax.plot(
        [], [], lw=4, ls="--", color="white", alpha=0.3, zorder=1
    )[0]
    investment_line = ax.plot(
        [], [], lw=2, ls="--", color="white", label=investment_col_name, zorder=2
    )[0]

    all_stock_lines = [line for group in stock_line_groups.values() for line in group]
    all_lines = all_stock_lines + [investment_line_glow, investment_line]

    # Timestamp positioned for optimal visibility across platforms
    date_text = ax.text(
        0.96,  # Moved slightly left for better clearance
        0.08,  # Moved up slightly from bottom
        "",
        transform=ax.transAxes,
        fontsize=28,  # Slightly smaller for better proportion
        color="#ffffff",
        ha="right",
        va="bottom",
        fontname="monospace",
        weight="bold",
        # Add subtle shadow effect for better readability
        path_effects=[path_effects.withStroke(linewidth=3, foreground="#0d1117")],
    )

    # --- Enhanced Styling for Social Media Optimization ---
    wrapped_title = "\n".join(
        textwrap.wrap(title, width=35)
    )  # Slightly narrower for better fit
    ax.text(
        0.5,
        0.92,  # Lowered to avoid TikTok UI elements
        wrapped_title,
        transform=ax.transAxes,
        fontsize=20,  # Slightly smaller for better proportion
        color="#ffffff",
        weight="bold",
        ha="center",
        va="top",
        # Add stroke effect for better readability (replaces glow)
        path_effects=[
            path_effects.withStroke(linewidth=4, foreground="#0d1117", alpha=0.8),
            path_effects.withStroke(linewidth=2, foreground="#30363d", alpha=0.6),
        ],
    )

    # Enhanced legend with better positioning and styling
    legend = ax.legend(
        loc="upper left",
        bbox_to_anchor=(0.03, 0.82),  # Adjusted for new margins
        fontsize=15,  # Increased from 13 for better visibility
        frameon=True,
        facecolor="#252b3d",  # Lighter than background for better contrast
        edgecolor="#4a5568",  # More visible border
        labelcolor="white",
        framealpha=0.95,  # Slightly more opaque
        borderpad=1.0,  # Increased padding for larger appearance
    )
    # Add subtle stroke effect to legend frame for better definition
    legend.get_frame().set_linewidth(2.0)  # Slightly thicker border
    legend.get_frame().set_edgecolor("#4a5568")

    ax.axes.get_xaxis().set_visible(False)
    ax.tick_params(
        axis="y", colors="#a0aec0", labelsize=13
    )  # Lighter gray for better visibility
    ax.yaxis.set_major_formatter(
        FuncFormatter(lambda x, _: f"${x:,.0f}")
    )  # Fix unused pos parameter

    # Enhanced grid with better visibility
    ax.grid(alpha=0.2, color="#4a5568", linewidth=0.6)  # More visible grid
    ax.set_axisbelow(True)  # Ensure grid stays behind data

    # Apply dynamic margins with platform optimization
    fig.subplots_adjust(
        left=dynamic_left_margin,
        right=RIGHT_MARGIN,
        top=TOP_MARGIN,
        bottom=BOTTOM_MARGIN,
    )

    # --- Final Summary Data ---
    final_wealth = stock_wealth_df.iloc[-1]
    cagr = (final_wealth / total_investment) ** (1 / investment_years) - 1
    cagr_sorted = cagr.sort_values(ascending=False)

    asset_returns = stock_wealth_df.pct_change()
    volatility = asset_returns.std()

    # Enhanced summary text with better positioning and styling
    # Set high z-order to ensure summary appears above all lines
    summary_text_obj = ax.text(
        0.5,
        0.45,  # Slightly higher to avoid platform UI elements
        "",
        transform=ax.transAxes,
        fontsize=13,  # Slightly smaller for better fit
        color="#ffffff",
        ha="center",
        va="center",
        fontname="monospace",
        alpha=0,
        zorder=100,  # High z-order to appear above all other elements
        bbox=dict(
            boxstyle="round,pad=0.8", fc="#252b3d", ec="#4a5568", alpha=0, linewidth=1.8
        ),
        # Add stroke effect for better readability
        path_effects=[
            path_effects.withStroke(linewidth=2, foreground="#1a1f2e", alpha=0.8)
        ],
    )
    summary_artists = [summary_text_obj]

    def update(frame_num):
        """Enhanced animation update function with improved visual effects."""
        if frame_num < main_animation_frames:
            data_idx = main_frame_indices[frame_num]
            current_data = wealth_df.iloc[: data_idx + 1]

            # Update all stock line groups (including enhanced glow layers)
            for ticker, line_group in stock_line_groups.items():
                for line in line_group:
                    line.set_data(current_data.index, current_data[ticker])

            # Update investment line with glow effect
            investment_line_glow.set_data(
                current_data.index, current_data[investment_col_name]
            )
            investment_line.set_data(
                current_data.index, current_data[investment_col_name]
            )

            # Dynamic y-axis scaling with improved margins
            y_max = (
                current_data.iloc[:, :-1].max().max() * 1.15
            )  # Reduced multiplier for better fit
            current_min_wealth = stock_wealth_df.iloc[: data_idx + 1].min().min()
            y_min = (
                min(first_investment_val, current_min_wealth) * 0.98
            )  # Tighter bottom margin

            # Enhanced x-axis with better time padding
            ax.set_xlim(
                wealth_df.index.min() - pd.Timedelta(days=30),  # Small left padding
                current_data.index.max()
                + pd.Timedelta(days=60),  # Reduced right padding
            )
            ax.set_ylim(y_min, y_max if y_max > y_min else y_min + 1)

            # Enhanced date formatting with better visual appeal
            current_date = current_data.index.max()
            formatted_date = current_date.strftime("%b %Y")  # More readable format
            date_text.set_text(formatted_date)
        else:
            # Enhanced summary section with better formatting and visual appeal
            if frame_num == main_animation_frames:
                rank_width = 6
                ticker_width = max(
                    8, max(len(ticker) for ticker in cagr_sorted.index) + 2
                )
                wealth_width = max(
                    12,
                    max(
                        len(f"${final_wealth[ticker]:,.0f}")
                        for ticker in cagr_sorted.index
                    )
                    + 2,
                )
                cagr_width = 8
                volatility_width = 12

                # Enhanced header with better visual separation
                header = (
                    f"{'Rank':<{rank_width}}"
                    f"{'Asset':<{ticker_width}}"  # Changed from 'Ticker' for better readability
                    f"{'Final Value':>{wealth_width}}"  # Changed from 'Final Wealth'
                    f"{'CAGR':>{cagr_width}}"
                    f"{'Risk':>{volatility_width}}"  # Changed from 'Volatility' for brevity
                )
                # Enhanced separator with premium visual appeal
                separator = "═" * len(header)

                body_lines = []
                for i, (ticker, cagr_val) in enumerate(cagr_sorted.items()):
                    rank_str = f"#{i+1}"
                    wealth_str = f"${final_wealth[ticker]:,.0f}"
                    cagr_str = f"{cagr_val:.1%}"
                    volatility_str = f"{volatility[ticker]:.1%}"

                    line = (
                        f"{rank_str:<{rank_width}}"
                        f"{ticker:<{ticker_width}}"
                        f"{wealth_str:>{wealth_width}}"
                        f"{cagr_str:>{cagr_width}}"
                        f"{volatility_str:>{volatility_width}}"
                    )
                    body_lines.append(line)

                body = "\n".join(body_lines)

                # Enhanced footer with better visual separation
                footer_separator = "─" * len(header)
                footer_string = f"Total Invested: ${total_investment:,.0f}"

                # Enhanced summary with clean text-only formatting
                # No icons/emojis for maximum compatibility and clean appearance
                summary_string = (
                    f"PERFORMANCE SUMMARY\n"  # Clean text header
                    f"{separator}\n"
                    f"{header}\n"
                    f"{separator}\n"
                    f"{body}\n"
                    f"{footer_separator}\n"
                    f"{footer_string}"
                )

                summary_text_obj.set_text(summary_string)

            # Enhanced fade-in with smoother easing for premium feel
            fade_in_progress = (frame_num - main_animation_frames) / summary_frames
            # Apply quadratic easing for smoother animation
            alpha_value = min(1.0, (fade_in_progress**0.8) * 1.4)

            summary_text_obj.set_alpha(alpha_value)
            # Enhanced background opacity for better readability and prominence
            # Higher opacity to ensure summary is clearly visible above lines
            summary_text_obj.get_bbox_patch().set_alpha(min(0.95, alpha_value * 0.98))

        # Return summary_artists last to ensure they're drawn on top
        return all_lines + [date_text] + summary_artists

    # --- Create and Save ---
    LOGGER.info(f"Creating {duration_sec + SUMMARY_FADE_IN_SEC}-second animation ...")
    ani = FuncAnimation(
        fig,
        update,
        frames=total_animation_frames,
        init_func=lambda: all_lines + [date_text] + summary_artists,
        blit=False,
    )

    music_manager = MusicManager(music_dir)
    final_filename = filename
    temp_video_path = None

    if music_manager.has_music():
        temp_video_path = Path(tempfile.mktemp(suffix=".mp4"))
        ani.save(str(temp_video_path), writer="ffmpeg", fps=fps, dpi=300)
        LOGGER.info(f"Temporary animation saved as '{temp_video_path}'")

        video_duration = duration_sec + SUMMARY_FADE_IN_SEC
        selected_music = music_manager.select_music(music_filename)

        if music_manager.add_music_to_video(
            temp_video_path, Path(final_filename), selected_music, video_duration
        ):
            temp_video_path.unlink()
            attribution = music_manager.get_music_attribution(selected_music)
            if attribution:
                LOGGER.info(f"Music attribution: {attribution}")
        else:
            temp_video_path.rename(final_filename)
            LOGGER.warning("Music integration failed, saved video without audio")
    else:
        ani.save(filename, writer="ffmpeg", fps=fps, dpi=300)
        LOGGER.info("No music available, saved video without audio")

    LOGGER.info(f"Animation successfully saved as '{final_filename}'")
    plt.close(fig)
