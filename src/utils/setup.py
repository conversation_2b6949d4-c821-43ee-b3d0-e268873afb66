"""Setup utilities for ReelStonks project.

This module provides utilities for setting up the project directory structure
and ensuring all necessary directories exist before running the application.
"""

from src.logger import get_logger
from src.utils.paths import (
    get_project_root,
    get_assets_dir,
    get_animations_dir,
    get_data_dir,
    get_options_dir,
    ensure_dir_exists,
    get_timestamped_animations_dir,
)


LOGGER = get_logger(__name__)


class ProjectSetup:
    """Handles project directory setup and initialization."""

    def __init__(self):
        """Initialize the ProjectSetup instance."""
        self.project_root = get_project_root()
        self.assets_dir = get_assets_dir()
        self.animations_dir = get_animations_dir()
        self.data_dir = get_data_dir()
        self.options_dir = get_options_dir()

        self.required_directories = [
            self.assets_dir,
            self.animations_dir,
            self.data_dir,
            self.options_dir,
            get_timestamped_animations_dir(),
        ]

    def setup_directories(self, verbose: bool = True) -> bool:
        """Set up all required project directories.

        Creates all necessary directories for the project to function properly.
        If directories already exist, they are left unchanged.

        Args:
            verbose (bool, optional): Whether to log setup progress. Defaults to True.

        Returns:
            bool: True if setup was successful, False otherwise.
        """
        if verbose:
            LOGGER.activate()
            LOGGER.info("🚀 Setting up ReelStonks project directories...")

        try:
            created_dirs = []
            existing_dirs = []

            for directory in self.required_directories:
                if directory.exists():
                    existing_dirs.append(directory)
                    if verbose:
                        LOGGER.info(
                            f"✅ Directory already exists: {directory.relative_to(self.project_root)}"
                        )
                else:
                    ensure_dir_exists(directory)
                    created_dirs.append(directory)
                    if verbose:
                        LOGGER.info(
                            f"📁 Created directory: {directory.relative_to(self.project_root)}"
                        )

            # Create example options if options directory is empty
            self._setup_example_options(verbose)

            if verbose:
                if created_dirs:
                    LOGGER.info(
                        f"🎉 Successfully created {len(created_dirs)} new directories"
                    )
                if existing_dirs:
                    LOGGER.info(f"ℹ️  Found {len(existing_dirs)} existing directories")
                LOGGER.info(
                    "✨ Project setup complete! Ready to create amazing investment animations."
                )

            return True

        except Exception as e:
            if verbose:
                LOGGER.error(f"❌ Failed to set up project directories: {e}")
            return False

    def _setup_example_options(self, verbose: bool = True) -> None:
        """Set up example option files if the options directory is empty.

        Args:
            verbose (bool): Whether to log setup progress.
        """
        if verbose:
            LOGGER.activate()

        if not self.options_dir.exists():
            return

        # Check if options directory has any .toml files (excluding those starting with _)
        toml_files = [
            f for f in self.options_dir.glob("*.toml") if not f.name.startswith("_")
        ]

        if toml_files:
            # Directory already has options, nothing to do
            return

        if verbose:
            LOGGER.warning(
                "📝 Options directory is empty, creating example option files..."
            )

        # Create two example option files
        self._create_tech_giants_example()
        self._create_dividend_focus_example()

        if verbose:
            LOGGER.info("✅ Created 2 example option files in assets/options/")

    def _create_tech_giants_example(self) -> None:
        """Create the tech giants example option file."""
        tech_giants_content = """# Tech Giants Showdown Option
# Compare Apple, Microsoft, Google, and Amazon with dividend strategies

[option]
id = "tech_giants"
display_text = "🏢 Tech Giants Showdown"
description = "Compare Apple, Microsoft, Google, and Amazon (all dividends reinvested)"

[strategies.AAPL_reinvest]
ticker = "AAPL"
name = "Apple"
reinvest_dividends = true
timing = "regular"

[strategies.MSFT_reinvest]
ticker = "MSFT"
name = "Microsoft"
reinvest_dividends = true
timing = "regular"

[strategies.GOOGL_regular]
ticker = "GOOGL"
name = "Google"
reinvest_dividends = true
timing = "regular"

[strategies.AMZN_regular]
ticker = "AMZN"
name = "Amazon"
reinvest_dividends = true
timing = "regular"
"""

        tech_giants_file = self.options_dir / "tech_giants.toml"
        with open(tech_giants_file, "w", encoding="utf-8") as f:
            f.write(tech_giants_content)

    def _create_dividend_focus_example(self) -> None:
        """Create the dividend focus example option file."""
        dividend_focus_content = """# Dividend Strategy Focus Option
# Compare dividend reinvestment vs cash dividends for high-dividend stocks

[option]
id = "dividend_focus"
display_text = "💰 Dividend Strategy Focus"
description = "Compare dividend reinvestment vs cash dividends for high-dividend stocks"

[strategies.JNJ_reinvest]
ticker = "JNJ"
name = "Johnson & Johnson (Acc.)"
reinvest_dividends = true
timing = "regular"

[strategies.JNJ_cash]
ticker = "JNJ"
name = "Johnson & Johnson (Dist.)"
reinvest_dividends = false
timing = "regular"

[strategies.KO_reinvest]
ticker = "KO"
name = "Coca-Cola (Acc.)"
reinvest_dividends = true
timing = "regular"

[strategies.KO_cash]
ticker = "KO"
name = "Coca-Cola (Dist.)"
reinvest_dividends = false
timing = "regular"
"""

        dividend_focus_file = self.options_dir / "dividend_focus.toml"
        with open(dividend_focus_file, "w", encoding="utf-8") as f:
            f.write(dividend_focus_content)

    def verify_setup(self) -> bool:
        """Verify that all required directories exist.

        Returns:
            bool: True if all required directories exist, False otherwise.
        """
        for directory in self.required_directories:
            if not directory.exists():
                return False
        return True

    def get_setup_status(self) -> dict:
        """Get the current setup status.

        Returns:
            dict: Dictionary containing setup status information with keys:
                - 'is_setup': bool indicating if setup is complete
                - 'missing_dirs': list of missing directory paths
                - 'existing_dirs': list of existing directory paths
        """
        missing_dirs = []
        existing_dirs = []

        for directory in self.required_directories:
            if directory.exists():
                existing_dirs.append(directory)
            else:
                missing_dirs.append(directory)

        return {
            "is_setup": len(missing_dirs) == 0,
            "missing_dirs": missing_dirs,
            "existing_dirs": existing_dirs,
            "project_root": self.project_root,
        }

    def create_gitkeep_files(self) -> None:
        """Create .gitkeep files in empty directories.

        This ensures that empty directories are tracked by git while
        still ignoring the actual content files (as specified in .gitignore).
        """
        for directory in self.required_directories:
            if directory.exists():
                gitkeep_file = directory / ".gitkeep"
                if not gitkeep_file.exists():
                    gitkeep_file.touch()
                    LOGGER.info(
                        f"📝 Created .gitkeep in {directory.relative_to(self.project_root)}"
                    )


def setup_project(verbose: bool = True) -> bool:
    """Convenience function to set up the project.

    Args:
        verbose (bool, optional): Whether to log setup progress. Defaults to True.

    Returns:
        bool: True if setup was successful, False otherwise.
    """
    setup = ProjectSetup()
    return setup.setup_directories(verbose=verbose)


def verify_project_setup() -> bool:
    """Convenience function to verify project setup.

    Returns:
        bool: True if project is properly set up, False otherwise.
    """
    setup = ProjectSetup()
    return setup.verify_setup()


if __name__ == "__main__":
    # Allow running this module directly for setup
    setup_project(verbose=True)
