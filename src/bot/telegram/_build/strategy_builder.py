"""Modular strategy builder for interactive Telegram menu system."""

import time
from typing import Dict, Optional

from src.logger import get_logger
from src.bot.telegram.telegram import TelegramManager

LOGGER = get_logger(__name__)


class StrategyBuilder:
    """
    Modular builder for creating investment strategies via interactive Telegram prompts.
    """

    def __init__(self, telegram_manager: TelegramManager, timeout_seconds: int = 300):
        """
        Initialize the strategy builder.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance.
            timeout_seconds (int): Seconds to wait for each user response.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_seconds = timeout_seconds
        self.strategies = {}

        LOGGER.info("🏗️ Strategy builder initialized")

    def build_strategies_interactive(self) -> Dict[str, Dict[str, any]]:
        """
        Build strategies interactively through Telegram prompts.

        Returns:
            Dict[str, Dict[str, any]]: Dictionary of strategy configurations.
        """
        LOGGER.info("📋 Starting interactive strategy building")

        self.telegram_manager.send_message(
            "🏗️ **Strategy Builder**\n\n"
            "Let's build your investment strategies step by step!\n"
        )

        strategy_count = 1

        while True:
            try:
                self.telegram_manager.send_message(
                    f"📈 **Strategy #{strategy_count}**\n\n"
                    "Create your investment strategy!"
                )

                # Get ticker (required)
                ticker = self._ask_ticker_question()
                if not ticker:
                    LOGGER.info("⏰ No ticker provided, ending strategy building")
                    break

                # Get name with smart default
                name = self._ask_name_question(ticker)

                # Get dividend reinvestment
                reinvest_dividends = self._ask_dividend_question()

                # Get timing strategy
                timing = self._ask_timing_question()

                # Create strategy
                strategy_id = f"{ticker}_{strategy_count}"
                self.strategies[strategy_id] = {
                    "ticker": ticker,
                    "name": name,
                    "reinvest_dividends": reinvest_dividends,
                    "timing": timing,
                }

                self.telegram_manager.send_message(
                    f"✅ **Strategy #{strategy_count} created!**\n\n"
                    f"• Ticker: {ticker}\n"
                    f"• Name: {name}\n"
                    f"• Dividends: {'Reinvested' if reinvest_dividends else 'Cash'}\n"
                    f"• Timing: {timing.title()}"
                )

                # Ask if user wants to add another strategy
                add_another = self._ask_add_another_question()

                if not add_another:
                    break

                strategy_count += 1

                # Limit to reasonable number of strategies
                if strategy_count > 10:
                    self.telegram_manager.send_message(
                        "⚠️ Maximum of 10 strategies reached. Finishing strategy creation."
                    )
                    break

            except Exception as e:
                LOGGER.error(f"❌ Error building strategy #{strategy_count}: {e}")
                break

        LOGGER.info(f"✅ Built {len(self.strategies)} strategies")
        return self.strategies

    def _ask_ticker_question(self) -> Optional[str]:
        """Ask for ticker symbol with validation."""
        self.telegram_manager.send_message(
            "🏷️ **Stock Ticker Symbol**\n\n"
            "Enter the ticker symbol:\n\n"
            "**Examples:**\n"
            "• AAPL (Apple)\n"
            "• MSFT (Microsoft)\n"
            "• ^GSPC (S&P 500)\n"
            "• BTC-USD (Bitcoin)\n"
            "• ETH-USD (Ethereum)\n\n"
            "💡 Reply 'd' to end strategy building"
        )

        response = self._wait_for_response()
        if response is None:
            self.telegram_manager.send_message(
                "⏰ No response received, ending strategy building"
            )
            return None

        response = response.strip()
        if response.lower() == "d":
            return None

        # Normalize ticker input - make uppercase but preserve special characters
        if response.startswith("^"):
            # Index symbols like ^GSPC - keep as is but uppercase the letters
            response = "^" + response[1:].upper()
        elif "-" in response:
            # Crypto or other hyphenated symbols - uppercase everything
            response = response.upper()
        else:
            # Regular stock symbols - uppercase
            response = response.upper()

        return response

    def _ask_name_question(self, ticker: str) -> str:
        """Ask for display name with smart default."""
        # Create smart default based on ticker
        smart_default = self._get_smart_name_default(ticker)

        self.telegram_manager.send_message(
            f"📝 **Display Name**\n\n"
            f"Enter a friendly name for {ticker}:\n\n"
            f"**Default:** {smart_default}\n\n"
            "💡 Reply 'd' to use default"
        )

        response = self._wait_for_response()
        if response is None or response.strip().lower() == "d":
            self.telegram_manager.send_message(f"Using default: {smart_default}")
            return smart_default

        return response.strip()

    def _ask_dividend_question(self) -> bool:
        """Ask about dividend reinvestment."""
        # Create inline keyboard for yes/no choice
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Yes - Reinvest", "callback_data": "dividend_yes"},
                    {"text": "💰 No - Cash", "callback_data": "dividend_no"},
                ],
                [{"text": "🔧 Default (No)", "callback_data": "dividend_default"}],
            ]
        }

        self.telegram_manager.send_message(
            "💰 **Dividend Reinvestment**\n\n"
            "Should dividends be automatically reinvested?\n\n"
            "• **Yes**: Dividends buy more shares\n"
            "• **No**: Dividends are received as cash\n\n"
            "**Default: No**\n\n"
            "� **Click your choice:**",
            reply_markup=keyboard,
        )

        response = self._wait_for_response_with_callbacks()
        if response is None or response in ["d", "dividend_default"]:
            self.telegram_manager.send_message("Using default: No")
            return False

        if response in ["y", "yes", "1", "true", "dividend_yes"]:
            return True
        elif response in ["n", "no", "0", "false", "dividend_no"]:
            return False
        else:
            self.telegram_manager.send_message("⚠️ Invalid response, using default: No")
            return False

    def _ask_timing_question(self) -> str:
        """Ask about investment timing strategy."""
        # Create inline keyboard for timing options
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "📅 Regular", "callback_data": "timing_regular"},
                    {"text": "📈 Peaks", "callback_data": "timing_peaks"},
                ],
                [
                    {"text": "📉 Lows", "callback_data": "timing_lows"},
                    {"text": "🔧 Default (Regular)", "callback_data": "timing_default"},
                ],
            ]
        }

        self.telegram_manager.send_message(
            "⏰ **Investment Timing Strategy**\n\n"
            "When should investments be made?\n\n"
            "• **Regular** - Invest at regular intervals\n"
            "• **Peaks** - Only invest at monthly price peaks\n"
            "• **Lows** - Only invest at monthly price lows\n\n"
            "**Default: Regular**\n\n"
            "� **Click your choice:**",
            reply_markup=keyboard,
        )

        response = self._wait_for_response_with_callbacks()
        if response is None or response in ["d", "timing_default"]:
            self.telegram_manager.send_message("Using default: Regular")
            return "regular"

        # Handle callback responses
        if response in ["1", "regular", "r", "timing_regular"]:
            return "regular"
        elif response in ["2", "peaks", "p", "timing_peaks"]:
            return "peaks"
        elif response in ["3", "lows", "l", "timing_lows"]:
            return "lows"
        else:
            self.telegram_manager.send_message(
                "⚠️ Invalid choice, using default: Regular"
            )
            return "regular"

    def _ask_add_another_question(self) -> bool:
        """Ask if user wants to add another strategy."""
        # Create inline keyboard for yes/no choice
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "➕ Yes - Add Another", "callback_data": "add_yes"},
                    {"text": "✅ No - Finish", "callback_data": "add_no"},
                ],
                [{"text": "🔧 Default (No)", "callback_data": "add_default"}],
            ]
        }

        self.telegram_manager.send_message(
            "➕ **Add Another Strategy?**\n\n"
            "Do you want to add another investment strategy?\n\n"
            "• **Yes**: Add another strategy\n"
            "• **No**: Finish and create animation\n\n"
            "**Default: No**\n\n"
            "� **Click your choice:**",
            reply_markup=keyboard,
        )

        response = self._wait_for_response_with_callbacks()
        if response is None or response in [
            "d",
            "n",
            "no",
            "",
            "add_no",
            "add_default",
        ]:
            self.telegram_manager.send_message("✅ Finishing strategy creation")
            return False

        if response in ["y", "yes", "1", "add_yes"]:
            self.telegram_manager.send_message("✅ Adding another strategy")
            return True
        else:
            self.telegram_manager.send_message("✅ Finishing strategy creation")
            return False

    def _get_smart_name_default(self, ticker: str) -> str:
        """Generate smart default name based on ticker."""
        ticker_names = {
            "AAPL": "Apple",
            "MSFT": "Microsoft",
            "GOOGL": "Google",
            "AMZN": "Amazon",
            "TSLA": "Tesla",
            "JNJ": "Johnson & Johnson",
            "KO": "Coca-Cola",
            "BRK-B": "Berkshire Hathaway",
            "^GSPC": "S&P 500",
            "^IXIC": "Nasdaq",
            "^DJI": "Dow Jones",
            "BTC-USD": "Bitcoin",
            "ETH-USD": "Ethereum",
            "ADA-USD": "Cardano",
            "SOL-USD": "Solana",
            "SPY": "SPDR S&P 500 ETF",
            "QQQ": "Nasdaq 100 ETF",
            "VTI": "Total Stock Market ETF",
        }

        return ticker_names.get(ticker, ticker)

    def _wait_for_response(self) -> Optional[str]:
        """Wait for user response with timeout."""
        start_time = time.time()

        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()

            for update in updates:
                if "message" in update and "text" in update["message"]:
                    return update["message"]["text"]

            time.sleep(1)

        return None

    def _wait_for_response_with_callbacks(self) -> Optional[str]:
        """Wait for user response (text or button press) with timeout."""
        start_time = time.time()

        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()

            for update in updates:
                # Handle callback queries (button presses)
                if "callback_query" in update:
                    callback_query = update["callback_query"]
                    callback_data = callback_query.get("data", "")
                    callback_id = callback_query.get("id", "")

                    # Answer the callback query to remove loading state
                    self.telegram_manager.answer_callback_query(callback_id)

                    return callback_data

                # Handle text messages (fallback)
                elif "message" in update and "text" in update["message"]:
                    return update["message"]["text"].strip().lower()

            time.sleep(1)

        return None
