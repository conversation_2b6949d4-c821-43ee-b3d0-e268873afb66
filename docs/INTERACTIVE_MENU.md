# 🎛️ ReelStonks Interactive Menu System

The Interactive Menu System allows users to select different investment strategy configurations via Telegram before the animation is created. This feature provides a convenient way to quickly switch between different analysis scenarios without manually editing the config.toml file.

## ✨ Features

- **🎯 Interactive Selection**: Choose from predefined strategy sets via Telegram
- **⏱️ Configurable Timeout**: Set how long to wait for user response
- **🔧 Default Fallback**: Automatically uses config.toml if no selection is made
- **📊 Predefined Options**: Common investment scenarios ready to use
- **🔄 Configuration Merging**: Seamlessly integrates with existing config system

## 🚀 Quick Start

### 1. Enable the Feature

Add to your `config.toml`:

```toml
[telegram]
token = "YOUR_BOT_TOKEN_HERE"
chat_id = "YOUR_CHAT_ID_HERE"
enabled = true
interactive_menu = true
interactive_timeout_minutes = 5
```

### 2. Run ReelStonks

```bash
python main.py
```

When you start ReelStonks, you'll receive a Telegram message with menu options:

```
🎯 ReelStonks Interactive Menu

⏱️ You have 5 minutes to choose an option.
If no selection is made, the default configuration will be used.

📋 Available Options:

1. 🔧 Default Configuration
   Use the current config.toml settings without changes

2. 🏢 Tech Giants Showdown
   Compare Apple, Microsoft, Google, and Amazon with dividend strategies

3. 📊 Market Indices Battle
   Compare S&P 500, Nasdaq, and Dow Jones performance

4. ⏰ Timing Strategy Test
   Compare regular investing vs buying at peaks vs buying at lows (S&P 500)

5. 💰 Dividend Strategy Focus
   Compare dividend reinvestment vs cash dividends for high-dividend stocks

💬 How to respond:
Reply with the number (1-5) of your choice.
Example: Send '1' for the first option.

⚡ Quick tip: Option 1 is always the default configuration!
```

### 3. Make Your Selection

Simply reply with the number of your choice (e.g., `2` for Tech Giants Showdown).

## 📋 Available Menu Options

The interactive menu system now uses TOML files for configuration, making it easy to customize and extend.

### Predefined Options

### 1. 🔧 Default Configuration
Uses your current `config.toml` settings without any changes.

### 2. 🏢 Tech Giants Showdown
Compares major tech companies:
- Apple (with dividend reinvestment)
- Microsoft (with dividend reinvestment)
- Google (regular)
- Amazon (regular)

### 3. 📊 Market Indices Battle
Compares major market indices:
- S&P 500
- Nasdaq
- Dow Jones

### 4. ⏰ Timing Strategy Test
Tests different investment timing strategies using S&P 500:
- Regular monthly investing
- Buying only at monthly peaks
- Buying only at monthly lows

### 5. 💰 Dividend Strategy Focus
Compares dividend strategies for high-dividend stocks:
- Johnson & Johnson (reinvested vs cash)
- Coca-Cola (reinvested vs cash)

### Interactive Options

### 6. 🎨 Custom Full Configuration
Interactive builder that lets you specify:
- **General settings**: Years of data, investment amount, frequency, animation duration
- **Custom strategies**: Build strategies step-by-step with guided prompts

### 7. 📋 Custom Strategies Only
Interactive builder that:
- **Keeps your general config**: Uses existing settings from config.toml
- **Builds custom strategies**: Interactive strategy creation with prompts for:
  - Ticker symbol (e.g., AAPL, MSFT, ^GSPC)
  - Display name
  - Dividend reinvestment (yes/no)
  - Timing strategy (regular/peaks/lows)
  - Option to add multiple strategies

## ⚙️ Configuration Options

### Basic Configuration

```toml
[telegram]
enabled = true                    # Enable Telegram integration
interactive_menu = true           # Enable interactive menu
interactive_timeout_minutes = 5   # Minutes to wait for response
```

### Advanced Settings

You can customize the timeout and enable/disable the feature:

- **`interactive_menu`**: Set to `false` to disable the interactive menu
- **`interactive_timeout_minutes`**: How long to wait for user response (default: 5 minutes)

## 🔧 How It Works

1. **Startup**: When ReelStonks starts, it checks if Telegram and interactive menu are enabled
2. **Option Loading**: Loads menu options from TOML files in `assets/options/`
3. **Menu Display**: Sends a formatted menu message to your Telegram chat
4. **User Response**: Waits for your numeric selection within the timeout period
5. **Option Processing**:
   - **Predefined options**: Uses strategies from TOML files
   - **Interactive options**: Launches interactive strategy builder
6. **Configuration Merge**: Merges your selection with the base config.toml settings
7. **Validation**: Validates the merged configuration before proceeding
8. **Animation Creation**: Creates the animation using the selected strategy configuration

## 🏗️ Interactive Strategy Builder

When you select an interactive option (Custom Full Configuration or Custom Strategies Only), the system launches an interactive builder that guides you through creating custom strategies.

### Interactive Flow

#### For Custom Strategies Only:
1. **Strategy Creation Loop**:
   ```
   📈 Strategy #1
   🏷️ Stock ticker symbol: AAPL
   📝 Display name for AAPL: Apple Inc.
   💰 Dividend reinvestment? [y/n]: y
   ⏰ Investment timing strategy:
      1. Regular - Invest at regular intervals
      2. Peaks - Only invest at monthly price peaks
      3. Lows - Only invest at monthly price lows
      Default: Regular (1): 1
   ✅ Strategy #1 created!
   ➕ Add another strategy? [y/n]: y
   ```

2. **Repeat** until user chooses not to add more strategies

#### For Custom Full Configuration:
1. **General Settings**:
   ```
   📅 Years of historical data: 10
   💰 Investment amount per period: 1000
   📊 Investment frequency:
      1. Daily  2. Weekly  3. Monthly  4. Quarterly  5. Yearly
      Default: Monthly (3): 3
   🎬 Animation duration (seconds): 15
   ```

2. **Strategy Creation** (same as above)

### Interactive Features

- **⏱️ Timeout Handling**: 5-minute timeout per question with sensible defaults
- **🔄 Input Validation**: Validates ticker symbols, numbers, and choices
- **📝 Smart Defaults**: Provides reasonable defaults for all questions
- **🛡️ Error Recovery**: Graceful handling of invalid inputs
- **➕ Multiple Strategies**: Add as many strategies as needed (up to 10)

## 🧪 Testing

Test the interactive menu system:

```bash
python test_interactive_menu.py
```

This will:
- ✅ Check if Telegram is properly configured
- ✅ Display the interactive menu
- ✅ Wait for your selection
- ✅ Validate the configuration merging
- ✅ Confirm everything works correctly

## 🛠️ Customization

### TOML-Based Configuration

The menu options are now stored in TOML files in `assets/options/`. This makes it easy to add, modify, or remove options without changing code.

#### Option File Structure

Each TOML file defines one menu option:

```toml
# Basic option metadata
[option]
id = "my_custom_option"
display_text = "🎨 My Custom Strategy"
description = "Description of what this strategy does"
type = "strategies_only"  # or "use_config_file", "interactive_full", "interactive_strategies"

# Strategy definitions (for type = "strategies_only")
[strategies.CUSTOM_STRATEGY]
ticker = "AAPL"
name = "My Custom Apple Strategy"
reinvest_dividends = true
timing = "regular"
```

#### Option Types

- **`use_config_file`**: Uses the current config.toml without changes
- **`strategies_only`**: Replaces strategies with predefined ones
- **`interactive_full`**: Triggers interactive builder for full configuration
- **`interactive_strategies`**: Triggers interactive builder for strategies only

### Adding New Predefined Options

Create a new TOML file in `assets/options/`:

```toml
# assets/options/crypto_comparison.toml
[option]
id = "crypto_comparison"
display_text = "₿ Cryptocurrency Comparison"
description = "Compare Bitcoin, Ethereum, and other major cryptocurrencies"
type = "strategies_only"

[strategies.BTC_regular]
ticker = "BTC-USD"
name = "Bitcoin"
reinvest_dividends = false
timing = "regular"

[strategies.ETH_regular]
ticker = "ETH-USD"
name = "Ethereum"
reinvest_dividends = false
timing = "regular"
```

### Adding New Menu Options (Legacy Method)

You can still extend the menu system programmatically:

```python
# Add a new option in create_predefined_options()
self.add_option(
    option_id="my_custom_option",
    display_text="🎨 My Custom Strategy",
    description="Description of what this strategy does",
    strategy_data={
        "strategies": {
            "CUSTOM_STRATEGY": {
                "ticker": "AAPL",
                "name": "My Custom Apple Strategy",
                "reinvest_dividends": True,
                "timing": "regular"
            }
        }
    }
)
```

### Custom Timeout Handling

The system automatically falls back to the default configuration if:
- No response is received within the timeout period
- An invalid selection is made
- Any error occurs during the process

## 🔍 Troubleshooting

### Common Issues

**"Interactive menu not showing"**
- Check that `telegram.enabled = true` in config.toml
- Verify `telegram.interactive_menu = true`
- Ensure your bot token and chat ID are correct

**"Menu timeout too short/long"**
- Adjust `telegram.interactive_timeout_minutes` in config.toml
- Default is 5 minutes, you can set any value

**"Invalid selection error"**
- Make sure to send only the number (1-5)
- Don't include extra text or characters

### Debug Mode

Enable debug logging to see what's happening:

```python
import logging
logging.getLogger('src.bot').setLevel(logging.DEBUG)
```

## 🤝 Integration

The interactive menu system integrates seamlessly with:
- ✅ Existing config.toml settings
- ✅ All investment strategies (regular, peaks, lows)
- ✅ Dividend reinvestment options
- ✅ Tax calculations
- ✅ Animation creation
- ✅ Telegram notifications

Your base configuration in `config.toml` is never modified - the menu selections are merged at runtime only.

## 📈 Benefits

- **⚡ Quick Testing**: Rapidly test different investment scenarios
- **🎯 User-Friendly**: No need to manually edit configuration files
- **🔄 Flexible**: Easy to switch between different analysis approaches
- **📱 Mobile-Friendly**: Control everything from your phone via Telegram
- **🛡️ Safe**: Original configuration is never modified
