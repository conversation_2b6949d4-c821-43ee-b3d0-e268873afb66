[project]
name = "reelstonks"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "black>=25.1.0",
    "ffmpeg-python>=0.2.0",
    "matplotlib>=3.10.3",
    "numba>=0.61.2",
    "ollama>=0.5.1",
    "pandas>=2.3.0",
    "pytest>=8.4.1",
    "python-dateutil>=2.9.0.post0",
    "tiktok-uploader>=1.1.1",
    "toml>=0.10.2",
    "yfinance>=0.2.64",
]

[tool.pytest.ini_options]
testpaths = ["src/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
